"""
Clean and Simple PDF Content Extractor
Extracts content from PDF and organizes it into structured JSON format exactly as requested
"""

import json
import re
from pathlib import Path
from typing import Dict, List, Any, Optional
import pdfplumber
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class CleanPDFExtractor:
    """Simple PDF content extractor with clean JSON output"""
    
    def __init__(self):
        self.current_section = None
        self.current_subsection = None
        
    def extract_pdf_to_json(self, pdf_path: str, output_json: str = "extracted.json") -> Dict[str, Any]:
        """Extract PDF content to structured JSON"""
        if not Path(pdf_path).exists():
            raise FileNotFoundError(f"PDF file not found: {pdf_path}")
        
        # Create images directory for charts
        image_dir = Path("images")
        image_dir.mkdir(exist_ok=True)
        
        result = {"pages": []}
        
        with pdfplumber.open(pdf_path) as pdf:
            for page_num, page in enumerate(pdf.pages, start=1):
                logger.info(f"Processing page {page_num}")
                page_data = self._extract_page_content(page, page_num, image_dir)
                result["pages"].append(page_data)
        
        # Save to JSON
        with open(output_json, "w", encoding="utf-8") as f:
            json.dump(result, f, indent=2, ensure_ascii=False)
        
        logger.info(f"✅ Extraction completed! Output saved to {output_json}")
        return result
    
    def _extract_page_content(self, page, page_num: int, image_dir: Path) -> Dict[str, Any]:
        """Extract content from a single page"""
        page_data = {"page_number": page_num, "content": []}
        
        # Reset section tracking for each page
        self.current_section = None
        self.current_subsection = None
        
        # 1. Extract text content with section detection
        text_content = self._extract_text_content(page)
        page_data["content"].extend(text_content)
        
        # 2. Extract tables
        table_content = self._extract_tables(page)
        page_data["content"].extend(table_content)
        
        # 3. Extract charts/images
        chart_content = self._extract_charts(page, page_num, image_dir)
        page_data["content"].extend(chart_content)
        
        return page_data
    
    def _extract_text_content(self, page) -> List[Dict[str, Any]]:
        """Extract and classify text content"""
        content = []
        text = page.extract_text() or ""
        
        if not text.strip():
            return content
        
        # Split into lines and process
        lines = text.split('\n')
        current_paragraph = []
        
        for line in lines:
            line = line.strip()
            
            # Check if this line is a section header
            if self._is_section_header(line):
                # Save current paragraph if exists
                if current_paragraph:
                    para_text = '\n'.join(current_paragraph)
                    content.append({
                        "type": "paragraph",
                        "section": self.current_section,
                        "sub_section": self.current_subsection,
                        "text": para_text
                    })
                    current_paragraph = []
                
                # Update current section
                self.current_section = line
                self.current_subsection = None
                continue
            
            # Check if this line is a subsection header
            if self._is_subsection_header(line):
                # Save current paragraph if exists
                if current_paragraph:
                    para_text = '\n'.join(current_paragraph)
                    content.append({
                        "type": "paragraph",
                        "section": self.current_section,
                        "sub_section": self.current_subsection,
                        "text": para_text
                    })
                    current_paragraph = []
                
                # Update current subsection
                self.current_subsection = line
                continue
            
            # Regular line - add to current paragraph
            if line:
                current_paragraph.append(line)
            else:
                # Empty line - end current paragraph
                if current_paragraph:
                    para_text = '\n'.join(current_paragraph)
                    content.append({
                        "type": "paragraph",
                        "section": self.current_section,
                        "sub_section": self.current_subsection,
                        "text": para_text
                    })
                    current_paragraph = []
        
        # Add final paragraph if exists
        if current_paragraph:
            para_text = '\n'.join(current_paragraph)
            content.append({
                "type": "paragraph",
                "section": self.current_section,
                "sub_section": self.current_subsection,
                "text": para_text
            })
        
        return content
    
    def _is_section_header(self, text: str) -> bool:
        """Check if text is a main section header"""
        if not text or len(text) > 100:
            return False
        
        # Main section patterns
        patterns = [
            r'^MONTHLY\s+FACTSHEET$',
            r'^MONTHLY\s+MARKET\s+UPDATE$',
            r'^\d+\s+ONE\s+.*FUND$',
            r'^GLOSSARY$',
            r'^DISCLAIMER$',
            r'^[A-Z][A-Z\s&-]+$'  # ALL CAPS (short)
        ]
        
        return any(re.match(pattern, text.strip()) for pattern in patterns)
    
    def _is_subsection_header(self, text: str) -> bool:
        """Check if text is a subsection header"""
        if not text or len(text) > 80:
            return False
        
        patterns = [
            r'^Equity\s+Market$',
            r'^Debt\s+Market$',
            r'^Portfolio\s+as\s+on',
            r'^Investment\s+Objective$',
            r'^Fund\s+Details$',
            r'^Scheme\s+Performance$',
            r'^Outlook:?$',
            r'^[A-Z][a-z]+\s+[A-Z][a-z]+:?$'  # Title Case
        ]
        
        return any(re.match(pattern, text.strip()) for pattern in patterns)
    
    def _extract_tables(self, page) -> List[Dict[str, Any]]:
        """Extract tables from the page"""
        content = []
        tables = page.extract_tables()
        
        for table in tables:
            if table and len(table) > 0:
                # Clean the table data
                clean_table = []
                for row in table:
                    clean_row = [cell.strip() if cell else "" for cell in row]
                    clean_table.append(clean_row)
                
                content.append({
                    "type": "table",
                    "section": self.current_section,
                    "sub_section": self.current_subsection,
                    "description": None,
                    "table_data": clean_table
                })
        
        return content
    
    def _extract_charts(self, page, page_num: int, image_dir: Path) -> List[Dict[str, Any]]:
        """Extract charts/images from the page"""
        content = []
        
        for i, img in enumerate(page.images, start=1):
            try:
                # Extract and save image
                bbox = (img["x0"], img["top"], img["x1"], img["bottom"])
                pil_image = page.to_image(resolution=150).original.crop(bbox)
                
                img_path = image_dir / f"page{page_num}_chart{i}.png"
                pil_image.save(img_path)
                
                # Create chart entry (without OCR for simplicity)
                content.append({
                    "type": "chart",
                    "section": self.current_section,
                    "sub_section": self.current_subsection,
                    "image_path": str(img_path),
                    "table_data": [],  # Empty for now - can be enhanced with OCR
                    "description": f"Chart extracted from page {page_num}"
                })
                
            except Exception as e:
                logger.warning(f"Failed to process chart {i} on page {page_num}: {e}")
        
        return content


def main():
    """Main function to run the PDF extractor"""
    extractor = CleanPDFExtractor()
    
    # Extract PDF content
    pdf_file = "xyz.pdf"  # Change to your PDF file
    output_file = "clean_extracted.json"
    
    try:
        result = extractor.extract_pdf_to_json(pdf_file, output_file)
        
        # Print summary
        total_pages = len(result["pages"])
        total_content = sum(len(page["content"]) for page in result["pages"])
        
        print(f"\n📊 Extraction Summary:")
        print(f"   Total pages: {total_pages}")
        print(f"   Total content blocks: {total_content}")
        
        # Count by type
        type_counts = {}
        sections_found = set()
        
        for page in result["pages"]:
            for item in page["content"]:
                item_type = item["type"]
                type_counts[item_type] = type_counts.get(item_type, 0) + 1
                
                if item.get("section"):
                    sections_found.add(item["section"])
        
        for content_type, count in type_counts.items():
            print(f"   {content_type.title()}s: {count}")
        
        print(f"\n📋 Sections found: {len(sections_found)}")
        for section in sorted(sections_found):
            print(f"   - {section}")
        
    except Exception as e:
        logger.error(f"Extraction failed: {e}")


if __name__ == "__main__":
    main()
