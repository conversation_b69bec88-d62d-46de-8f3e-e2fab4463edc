import pdfplumber
import pytesseract
from PIL import Image, ImageEnhance, ImageFilter, ImageOps
import json
import re
import cv2
import numpy as np
from pathlib import Path
import os
import sys
import subprocess
import warnings
from typing import Dict, List, Optional, Tuple, Any
import easyocr
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class TextClassifier:
    """Advanced text classification for identifying headings, subheadings, and content types"""

    def __init__(self):
        # Common heading patterns
        self.heading_patterns = [
            r"^[A-Z][A-Z\s&-]+$",  # ALL CAPS headings
            r"^[A-Z][a-z]+(?:\s+[A-Z][a-z]+)*:?\s*$",  # Title Case headings
            r"^\d+\.\s+[A-Z]",  # Numbered headings (1. Introduction)
            r"^[A-Z]+\s+[A-Z]+",  # Multiple caps words
            r"^MONTHLY\s+",  # Document-specific patterns
            r"^FACTSHEET",
            r"^MARKET\s+UPDATE",
            r"^Equity\s+Market",
            r"^Debt\s+Market",
            r"^Portfolio\s+",
            r"^Fund\s+",
            r"^Scheme\s+",
            r"^Performance\s+",
        ]

        # Subheading patterns
        self.subheading_patterns = [
            r"^[A-Z][a-z]+(?:\s+[A-Z][a-z]+)*\s*:",  # Title Case with colon
            r"^[A-Z][a-z]+\s+[A-Z][a-z]+\s*$",  # Two title case words
            r"^\w+\s+\w+\s*:",  # Word Word: pattern
            r"^Outlook\s*:?",
            r"^Source\s*:",
            r"^Note\s*:",
            r"^Risk\s*:",
        ]

        # Title patterns (main document titles)
        self.title_patterns = [
            r"^MONTHLY\s+FACTSHEET",
            r"^\d+\s+ONE\s+",  # Fund names like "360 ONE"
            r"^[A-Z\s]+FUND$",
            r"^[A-Z\s]+SCHEME$",
        ]

        # Footer/metadata patterns
        self.footer_patterns = [
            r"Page\s*\|\s*\d+",
            r"June\s+\d{4}\s+Mutual\s+Fund",
            r"Source\s*:",
            r"^\*.*",  # Lines starting with asterisk
            r"^\#.*",  # Lines starting with hash
        ]

    def classify_text(self, text: str) -> Dict[str, Any]:
        """
        Classify text into different types with confidence scores

        Returns:
            Dict with type, confidence, and metadata
        """
        text = text.strip()
        if not text:
            return {"type": "empty", "confidence": 1.0, "text": text}

        # Check for titles first (highest priority)
        for pattern in self.title_patterns:
            if re.match(pattern, text, re.IGNORECASE):
                return {
                    "type": "title",
                    "confidence": 0.9,
                    "text": text,
                    "characteristics": ["title_case", "document_title"],
                }

        # Check for main headings
        for pattern in self.heading_patterns:
            if re.match(pattern, text):
                return {
                    "type": "heading",
                    "confidence": 0.85,
                    "text": text,
                    "characteristics": self._analyze_text_characteristics(text),
                }

        # Check for subheadings
        for pattern in self.subheading_patterns:
            if re.match(pattern, text):
                return {
                    "type": "subheading",
                    "confidence": 0.8,
                    "text": text,
                    "characteristics": self._analyze_text_characteristics(text),
                }

        # Check for footers/metadata
        for pattern in self.footer_patterns:
            if re.search(pattern, text):
                return {
                    "type": "footer",
                    "confidence": 0.9,
                    "text": text,
                    "characteristics": ["metadata", "footer"],
                }

        # Analyze text characteristics for further classification
        characteristics = self._analyze_text_characteristics(text)

        # Check if it's likely a heading based on characteristics
        if self._is_likely_heading(text, characteristics):
            return {
                "type": "heading",
                "confidence": 0.7,
                "text": text,
                "characteristics": characteristics,
            }

        # Check if it's likely a subheading
        if self._is_likely_subheading(text, characteristics):
            return {
                "type": "subheading",
                "confidence": 0.65,
                "text": text,
                "characteristics": characteristics,
            }

        # Check for list items
        if self._is_list_item(text):
            return {
                "type": "list_item",
                "confidence": 0.8,
                "text": text,
                "characteristics": characteristics,
            }

        # Default to paragraph
        return {
            "type": "paragraph",
            "confidence": 0.6,
            "text": text,
            "characteristics": characteristics,
        }

    def _analyze_text_characteristics(self, text: str) -> List[str]:
        """Analyze text characteristics for classification"""
        characteristics = []

        # Length characteristics
        if len(text) < 50:
            characteristics.append("short")
        elif len(text) > 200:
            characteristics.append("long")
        else:
            characteristics.append("medium")

        # Case characteristics
        if text.isupper():
            characteristics.append("all_caps")
        elif text.istitle():
            characteristics.append("title_case")
        elif text.islower():
            characteristics.append("lower_case")
        else:
            characteristics.append("mixed_case")

        # Punctuation characteristics
        if text.endswith(":"):
            characteristics.append("ends_with_colon")
        if text.endswith("."):
            characteristics.append("ends_with_period")
        if "(" in text and ")" in text:
            characteristics.append("has_parentheses")

        # Content characteristics
        if any(
            word in text.lower()
            for word in ["fund", "scheme", "portfolio", "investment"]
        ):
            characteristics.append("financial_content")
        if any(word in text.lower() for word in ["market", "equity", "debt", "bond"]):
            characteristics.append("market_content")
        if re.search(r"\d+%", text):
            characteristics.append("has_percentage")
        if re.search(r"\d+", text):
            characteristics.append("has_numbers")

        return characteristics

    def _is_likely_heading(self, text: str, characteristics: List[str]) -> bool:
        """Determine if text is likely a heading based on characteristics"""
        heading_indicators = 0

        if "short" in characteristics:
            heading_indicators += 1
        if "all_caps" in characteristics or "title_case" in characteristics:
            heading_indicators += 2
        if "ends_with_colon" in characteristics:
            heading_indicators += 1
        if (
            "financial_content" in characteristics
            or "market_content" in characteristics
        ):
            heading_indicators += 1
        if len(text.split()) <= 5:  # Short phrases are more likely headings
            heading_indicators += 1

        return heading_indicators >= 3

    def _is_likely_subheading(self, text: str, characteristics: List[str]) -> bool:
        """Determine if text is likely a subheading"""
        subheading_indicators = 0

        if "short" in characteristics or "medium" in characteristics:
            subheading_indicators += 1
        if "title_case" in characteristics:
            subheading_indicators += 1
        if "ends_with_colon" in characteristics:
            subheading_indicators += 2
        if len(text.split()) <= 8:  # Moderate length
            subheading_indicators += 1

        return subheading_indicators >= 3

    def _is_list_item(self, text: str) -> bool:
        """Check if text is a list item"""
        list_patterns = [
            r"^•\s+",  # Bullet points
            r"^-\s+",  # Dash bullets
            r"^\d+\.\s+",  # Numbered lists
            r"^[a-zA-Z]\.\s+",  # Lettered lists
        ]

        return any(re.match(pattern, text) for pattern in list_patterns)


class EnhancedOCR:
    """Enhanced OCR class with multiple engines and image preprocessing"""

    def __init__(self):
        self.tesseract_available = self._check_tesseract()
        self.easyocr_reader = None
        self._init_easyocr()

    def _check_tesseract(self) -> bool:
        """Check if Tesseract is available"""
        try:
            subprocess.run(["tesseract", "--version"], capture_output=True, check=True)
            return True
        except (subprocess.CalledProcessError, FileNotFoundError):
            logger.warning(
                "Tesseract not found. Install from: https://github.com/UB-Mannheim/tesseract/wiki"
            )
            return False

    def _init_easyocr(self):
        """Initialize EasyOCR as fallback"""
        try:
            self.easyocr_reader = easyocr.Reader(["en"])
            logger.info("EasyOCR initialized successfully")
        except Exception as e:
            logger.warning(f"EasyOCR initialization failed: {e}")
            self.easyocr_reader = None

    def preprocess_image(
        self,
        image: Image.Image,
        enhance_contrast: bool = True,
        denoise: bool = True,
        sharpen: bool = True,
        binarize: bool = True,
    ) -> Image.Image:
        """
        Advanced image preprocessing for better OCR accuracy
        """
        # Convert to RGB if needed
        if image.mode != "RGB":
            image = image.convert("RGB")

        # Convert PIL to OpenCV format
        cv_image = cv2.cvtColor(np.array(image), cv2.COLOR_RGB2BGR)

        # Convert to grayscale
        gray = cv2.cvtColor(cv_image, cv2.COLOR_BGR2GRAY)

        # Denoise
        if denoise:
            gray = cv2.fastNlMeansDenoising(gray)

        # Enhance contrast using CLAHE
        if enhance_contrast:
            clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
            gray = clahe.apply(gray)

        # Sharpen
        if sharpen:
            kernel = np.array([[-1, -1, -1], [-1, 9, -1], [-1, -1, -1]])
            gray = cv2.filter2D(gray, -1, kernel)

        # Binarization using adaptive threshold
        if binarize:
            gray = cv2.adaptiveThreshold(
                gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2
            )

        # Convert back to PIL
        processed_image = Image.fromarray(gray)

        return processed_image

    def extract_text_tesseract(
        self,
        image: Image.Image,
        config: str = "--psm 6 -c tessedit_char_whitelist=0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz.,%-+()[]{}:;/\\|\"'",
    ) -> str:
        """Extract text using Tesseract OCR"""
        if not self.tesseract_available:
            return ""

        try:
            # Preprocess image
            processed_image = self.preprocess_image(image)

            # Extract text
            text = pytesseract.image_to_string(processed_image, config=config)
            return text.strip()
        except Exception as e:
            logger.error(f"Tesseract OCR failed: {e}")
            return ""

    def extract_text_easyocr(self, image: Image.Image) -> str:
        """Extract text using EasyOCR"""
        if not self.easyocr_reader:
            return ""

        try:
            # Preprocess image
            processed_image = self.preprocess_image(image)

            # Convert to numpy array
            img_array = np.array(processed_image)

            # Extract text
            results = self.easyocr_reader.readtext(img_array)

            # Combine all detected text
            text_parts = []
            for bbox, text, confidence in results:
                if confidence > 0.5:  # Filter low confidence results
                    text_parts.append(text)

            return " ".join(text_parts)
        except Exception as e:
            logger.error(f"EasyOCR failed: {e}")
            return ""

    def extract_text(self, image: Image.Image, method: str = "auto") -> Dict[str, Any]:
        """
        Extract text using the best available method

        Args:
            image: PIL Image object
            method: 'tesseract', 'easyocr', or 'auto'

        Returns:
            Dictionary with extracted text and metadata
        """
        results = {
            "text": "",
            "method_used": "",
            "confidence": 0.0,
            "preprocessing_applied": True,
        }

        if method == "auto":
            # Try Tesseract first, then EasyOCR
            if self.tesseract_available:
                text = self.extract_text_tesseract(image)
                if text:
                    results["text"] = text
                    results["method_used"] = "tesseract"
                    results["confidence"] = 0.8  # Assume good confidence for Tesseract
                    return results

            # Fallback to EasyOCR
            if self.easyocr_reader:
                text = self.extract_text_easyocr(image)
                if text:
                    results["text"] = text
                    results["method_used"] = "easyocr"
                    results["confidence"] = (
                        0.7  # Assume moderate confidence for EasyOCR
                    )
                    return results

        elif method == "tesseract":
            text = self.extract_text_tesseract(image)
            results["text"] = text
            results["method_used"] = "tesseract"
            results["confidence"] = 0.8 if text else 0.0

        elif method == "easyocr":
            text = self.extract_text_easyocr(image)
            results["text"] = text
            results["method_used"] = "easyocr"
            results["confidence"] = 0.7 if text else 0.0

        return results

    def extract_chart_data(self, image: Image.Image) -> Dict[str, Any]:
        """
        Extract structured data from charts and graphs
        """
        # Get basic OCR text
        ocr_result = self.extract_text(image)

        # Analyze image for chart type
        chart_type = self._detect_chart_type(image)

        # Extract structured data based on chart type
        structured_data = self._extract_structured_data(
            image, ocr_result["text"], chart_type
        )

        return {
            "raw_text": ocr_result["text"],
            "chart_type": chart_type,
            "structured_data": structured_data,
            "ocr_method": ocr_result["method_used"],
            "confidence": ocr_result["confidence"],
        }

    def _detect_chart_type(self, image: Image.Image) -> str:
        """
        Detect the type of chart/graph in the image
        """
        # Convert to OpenCV format
        cv_image = cv2.cvtColor(np.array(image), cv2.COLOR_RGB2BGR)
        gray = cv2.cvtColor(cv_image, cv2.COLOR_BGR2GRAY)

        # Detect lines (for line charts)
        edges = cv2.Canny(gray, 50, 150, apertureSize=3)
        lines = cv2.HoughLines(edges, 1, np.pi / 180, threshold=100)

        # Detect rectangles (for bar charts)
        contours, _ = cv2.findContours(gray, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        rectangles = 0
        for contour in contours:
            approx = cv2.approxPolyDP(
                contour, 0.02 * cv2.arcLength(contour, True), True
            )
            if len(approx) == 4:
                rectangles += 1

        # Simple heuristics for chart type detection
        if lines is not None and len(lines) > 10:
            return "line_chart"
        elif rectangles > 5:
            return "bar_chart"
        else:
            return "table_or_mixed"

    def _extract_structured_data(
        self, image: Image.Image, text: str, chart_type: str
    ) -> Dict[str, Any]:
        """
        Extract structured data based on chart type
        """
        data = {
            "numbers": self._extract_numbers(text),
            "labels": self._extract_labels(text),
            "data_points": [],
            "table_data": None,
        }

        if chart_type == "table_or_mixed":
            data["table_data"] = self._extract_table_data(text)

        # Extract coordinate-based data for charts
        if chart_type in ["line_chart", "bar_chart"]:
            data["data_points"] = self._extract_data_points(text)

        return data

    def _extract_numbers(self, text: str) -> List[float]:
        """Extract all numbers from text"""
        import re

        # Pattern to match numbers (including decimals, percentages, negatives)
        number_pattern = r"-?\d+\.?\d*%?"
        numbers = re.findall(number_pattern, text)

        parsed_numbers = []
        for num in numbers:
            try:
                if "%" in num:
                    parsed_numbers.append(float(num.replace("%", "")))
                else:
                    parsed_numbers.append(float(num))
            except ValueError:
                continue

        return parsed_numbers

    def _extract_labels(self, text: str) -> List[str]:
        """Extract potential labels from text"""
        # Split text into words and filter out numbers
        words = text.split()
        labels = []

        for word in words:
            # Skip if it's purely numeric
            if not re.match(r"^-?\d+\.?\d*%?$", word):
                # Clean the word
                clean_word = re.sub(r"[^\w\s-]", "", word).strip()
                if clean_word and len(clean_word) > 1:
                    labels.append(clean_word)

        return labels

    def _extract_data_points(self, text: str) -> List[Dict[str, Any]]:
        """Extract data points (label-value pairs)"""
        lines = text.split("\n")
        data_points = []

        for line in lines:
            line = line.strip()
            if not line:
                continue

            # Look for patterns like "Label: Value" or "Label Value"
            parts = re.split(r"[:\s]+", line)
            if len(parts) >= 2:
                label = parts[0]
                value_str = parts[-1]

                # Try to extract numeric value
                try:
                    if "%" in value_str:
                        value = float(value_str.replace("%", ""))
                        unit = "%"
                    else:
                        value = float(re.findall(r"-?\d+\.?\d*", value_str)[0])
                        unit = ""

                    data_points.append({"label": label, "value": value, "unit": unit})
                except (ValueError, IndexError):
                    continue

        return data_points

    def _extract_table_data(self, text: str) -> Optional[List[List[str]]]:
        """Extract table data from text"""
        lines = text.split("\n")
        table_rows = []

        for line in lines:
            line = line.strip()
            if not line:
                continue

            # Split by multiple spaces or tabs (common in OCR table output)
            cells = re.split(r"\s{2,}|\t", line)
            if len(cells) > 1:
                table_rows.append([cell.strip() for cell in cells])

        return table_rows if table_rows else None


def extract_pdf_to_json(pdf_path, output_json, image_dir="images"):
    """
    Extracts text, tables, and images (with enhanced OCR) from a PDF and saves to JSON.
    """
    Path(image_dir).mkdir(exist_ok=True)
    result = {"pages": []}

    # Initialize enhanced OCR and text classifier
    ocr_engine = EnhancedOCR()
    text_classifier = TextClassifier()
    logger.info(
        f"OCR Engine initialized. Tesseract available: {ocr_engine.tesseract_available}"
    )

    with pdfplumber.open(pdf_path) as pdf:
        for page_num, page in enumerate(pdf.pages, start=1):
            page_data = {"page_number": page_num, "content": []}

            # --- 1) Extract text with advanced classification ---
            text = page.extract_text() or ""

            # Split text into lines first, then group into logical blocks
            lines = text.split("\n")
            text_blocks = []
            current_block = []

            for line in lines:
                line = line.strip()
                if not line:
                    if current_block:
                        text_blocks.append("\n".join(current_block))
                        current_block = []
                else:
                    current_block.append(line)

            # Add the last block if it exists
            if current_block:
                text_blocks.append("\n".join(current_block))

            # Classify each text block
            for block in text_blocks:
                if not block.strip():
                    continue

                # Use advanced text classification
                classification = text_classifier.classify_text(block)

                page_data["content"].append(
                    {
                        "type": classification["type"],
                        "text": classification["text"],
                        "confidence": classification["confidence"],
                        "characteristics": classification.get("characteristics", []),
                    }
                )

            # --- 2) Extract tables ---
            tables = page.extract_tables()
            for t in tables:
                page_data["content"].append({"type": "table", "table_data": t})

            # --- 3) Extract images (charts) and OCR them with enhanced processing ---
            for i, img in enumerate(page.images, start=1):
                try:
                    # Get image region and save
                    bbox = (img["x0"], img["top"], img["x1"], img["bottom"])
                    pil_image = page.to_image(resolution=300).original.crop(
                        bbox
                    )  # Higher resolution

                    img_path = Path(image_dir) / f"page{page_num}_chart{i}.png"
                    pil_image.save(img_path)

                    # Enhanced OCR with chart data extraction
                    chart_data = ocr_engine.extract_chart_data(pil_image)

                    page_data["content"].append(
                        {
                            "type": "chart",
                            "image_path": str(img_path),
                            "raw_ocr_text": chart_data["raw_text"],
                            "chart_type": chart_data["chart_type"],
                            "structured_data": chart_data["structured_data"],
                            "ocr_method": chart_data["ocr_method"],
                            "confidence": chart_data["confidence"],
                            "extraction_success": bool(chart_data["raw_text"]),
                        }
                    )

                    logger.info(
                        f"Processed chart {i} on page {page_num}: {chart_data['chart_type']} "
                        f"using {chart_data['ocr_method']} (confidence: {chart_data['confidence']:.2f})"
                    )

                except Exception as e:
                    logger.error(f"Failed to process chart {i} on page {page_num}: {e}")
                    page_data["content"].append(
                        {
                            "type": "chart",
                            "image_path": str(
                                Path(image_dir) / f"page{page_num}_chart{i}.png"
                            ),
                            "error": str(e),
                            "extraction_success": False,
                        }
                    )

            result["pages"].append(page_data)

    # Save JSON
    with open(output_json, "w", encoding="utf-8") as f:
        json.dump(result, f, indent=2, ensure_ascii=False)

    print(f"✅ Extraction completed! Output saved to {output_json}")

    # Print summary
    total_charts = sum(
        len([c for c in page["content"] if c["type"] == "chart"])
        for page in result["pages"]
    )
    successful_charts = sum(
        len(
            [
                c
                for c in page["content"]
                if c["type"] == "chart" and c.get("extraction_success", False)
            ]
        )
        for page in result["pages"]
    )

    print(f"📊 Chart Analysis Summary:")
    print(f"   Total charts found: {total_charts}")
    print(f"   Successfully processed: {successful_charts}")
    print(
        f"   Success rate: {(successful_charts/total_charts*100):.1f}%"
        if total_charts > 0
        else "   No charts found"
    )


# ----------- Run the script -----------
if __name__ == "__main__":
    pdf_file = "xyz.pdf"  # Change to your file name
    output_file = "extracted.json"
    extract_pdf_to_json(pdf_file, output_file)
