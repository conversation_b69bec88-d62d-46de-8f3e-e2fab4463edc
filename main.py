import pdfplumber
import pytesseract
from PIL import Image
import json
import re
from pathlib import Path


def extract_pdf_to_json(pdf_path, output_json, image_dir="images"):
    """
    Extracts text, tables, and images (with OCR) from a PDF and saves to JSON.
    """
    Path(image_dir).mkdir(exist_ok=True)
    result = {"pages": []}

    with pdfplumber.open(pdf_path) as pdf:
        for page_num, page in enumerate(pdf.pages, start=1):
            page_data = {"page_number": page_num, "content": []}

            # --- 1) Extract text and split into paragraphs ---
            text = page.extract_text() or ""
            paragraphs = re.split(r"\n\s*\n", text)  # split on blank lines

            for para in paragraphs:
                if not para.strip():
                    continue

                if para.isupper() and len(para) < 80:
                    page_data["content"].append(
                        {"type": "header", "text": para.strip()}
                    )
                else:
                    page_data["content"].append(
                        {"type": "paragraph", "text": para.strip()}
                    )

            # --- 2) Extract tables ---
            tables = page.extract_tables()
            for t in tables:
                page_data["content"].append({"type": "table", "table_data": t})

            # --- 3) Extract images (charts) and OCR them ---
            for i, img in enumerate(page.images, start=1):
                try:
                    # Get image region and save
                    bbox = (img["x0"], img["top"], img["x1"], img["bottom"])
                    pil_image = page.to_image(resolution=150).original.crop(bbox)

                    img_path = Path(image_dir) / f"page{page_num}_chart{i}.png"
                    pil_image.save(img_path)

                    # OCR text from chart
                    ocr_text = pytesseract.image_to_string(pil_image)

                    page_data["content"].append(
                        {
                            "type": "chart",
                            "image_path": str(img_path),
                            "ocr_text": ocr_text.strip() if ocr_text.strip() else None,
                        }
                    )
                except Exception as e:
                    page_data["content"].append(
                        {
                            "type": "chart",
                            "image_path": None,
                            "description": f"Failed to extract: {e}",
                        }
                    )

            result["pages"].append(page_data)

    # Save JSON
    with open(output_json, "w", encoding="utf-8") as f:
        json.dump(result, f, indent=2, ensure_ascii=False)

    print(f"✅ Extraction completed! Output saved to {output_json}")


# ----------- Run the script -----------
if __name__ == "__main__":
    pdf_file = "xyz.pdf"  # Change to your file name
    output_file = "extracted.json"
    extract_pdf_to_json(pdf_file, output_file)
