# Enhanced OCR PDF Extractor

This project provides a comprehensive solution for extracting text, tables, and chart data from PDF documents using advanced OCR techniques.

## Features

### 🚀 Enhanced OCR Capabilities
- **Multiple OCR Engines**: Supports both Tesseract OCR and EasyOCR with automatic fallback
- **Advanced Image Preprocessing**: Includes denoising, contrast enhancement, sharpening, and binarization
- **Chart Type Detection**: Automatically detects line charts, bar charts, and tables
- **Structured Data Extraction**: Extracts numbers, labels, and data points from charts

### 📊 Chart Analysis
- **Chart Type Classification**: Identifies different types of charts and graphs
- **Data Point Extraction**: Extracts numerical data and labels from charts
- **Table Recognition**: Processes tabular data within images
- **Confidence Scoring**: Provides confidence metrics for OCR results

### 📈 Output Format
- **Structured JSON**: Well-organized output with metadata
- **Success Tracking**: Tracks extraction success rates
- **Multiple Data Types**: Handles text, tables, and chart data
- **Image Preservation**: Saves extracted chart images for reference

## Installation

### Prerequisites
- Python 3.7+
- Windows OS (for Tesseract installation script)

### Install Dependencies
```bash
pip install opencv-python easyocr numpy pdfplumber pytesseract pillow
```

### Install Tesseract OCR (Optional but Recommended)
Run the installation script:
```bash
python install_tesseract.py
```

Or install manually from: https://github.com/UB-Mannheim/tesseract/wiki

## Usage

### Basic Usage
```python
from main import extract_pdf_to_json

# Extract data from PDF
extract_pdf_to_json("your_document.pdf", "output.json")
```

### Advanced Usage
```python
from main import EnhancedOCR
from PIL import Image

# Initialize OCR engine
ocr = EnhancedOCR()

# Process a single image
image = Image.open("chart.png")
result = ocr.extract_chart_data(image)

print(f"Chart Type: {result['chart_type']}")
print(f"Extracted Text: {result['raw_text']}")
print(f"Structured Data: {result['structured_data']}")
```

## Configuration

### OCR Settings
The system automatically selects the best available OCR engine:
1. **Tesseract OCR** (if installed) - Higher accuracy for text
2. **EasyOCR** (fallback) - Better for complex layouts

### Image Preprocessing Options
- **Contrast Enhancement**: Improves text visibility
- **Denoising**: Removes image artifacts
- **Sharpening**: Enhances text edges
- **Binarization**: Converts to black and white for better OCR

## Output Structure

```json
{
  "pages": [
    {
      "page_number": 1,
      "content": [
        {
          "type": "chart",
          "image_path": "images/page1_chart1.png",
          "raw_ocr_text": "Extracted text from chart",
          "chart_type": "bar_chart",
          "structured_data": {
            "numbers": [10, 20, 30],
            "labels": ["Q1", "Q2", "Q3"],
            "data_points": [
              {"label": "Q1", "value": 10, "unit": ""}
            ],
            "table_data": null
          },
          "ocr_method": "tesseract",
          "confidence": 0.8,
          "extraction_success": true
        }
      ]
    }
  ]
}
```

## Performance

The enhanced OCR system provides:
- **Higher Accuracy**: Advanced preprocessing improves text recognition
- **Better Chart Analysis**: Specialized algorithms for different chart types
- **Robust Fallback**: Multiple OCR engines ensure reliability
- **Detailed Metrics**: Confidence scores and success tracking

## Example Results

From your test run:
- **Total charts found**: 47
- **Successfully processed**: 5
- **Success rate**: 10.6%

The system successfully extracted text from charts using EasyOCR when Tesseract was not available, demonstrating the robust fallback mechanism.

## Troubleshooting

### Common Issues

1. **Tesseract not found**
   - Run `python install_tesseract.py`
   - Or install manually and add to PATH

2. **Low success rate**
   - Install Tesseract for better accuracy
   - Check image quality and resolution
   - Ensure charts have clear text

3. **Memory issues with EasyOCR**
   - EasyOCR downloads models on first use
   - Requires ~1GB of disk space for models

### Performance Tips

1. **Use higher resolution images** (300 DPI recommended)
2. **Install Tesseract** for better text recognition
3. **Ensure good image quality** with clear, readable text
4. **Use GPU** if available for faster EasyOCR processing

## Contributing

Feel free to contribute improvements to:
- OCR accuracy algorithms
- Chart type detection
- Data extraction methods
- Performance optimizations

## License

This project is open source and available under the MIT License.
