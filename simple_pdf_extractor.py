"""
Simple PDF Content Extractor
Extracts content from PDF and organizes it into a structured JSON format
"""

import json
import re
from pathlib import Path
from typing import Dict, List, Any, Optional
import pdfplumber
from PIL import Image
import pytesseract
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class SimplePDFExtractor:
    """Simple PDF content extractor with clean JSON output"""

    def __init__(self):
        self.current_section = None
        self.current_subsection = None

    def extract_pdf_to_json(
        self, pdf_path: str, output_json: str = "extracted.json"
    ) -> Dict[str, Any]:
        """
        Extract PDF content to structured JSON

        Args:
            pdf_path: Path to PDF file
            output_json: Output JSON file path

        Returns:
            Dictionary containing extracted content
        """
        if not Path(pdf_path).exists():
            raise FileNotFoundError(f"PDF file not found: {pdf_path}")

        # Create images directory for charts
        image_dir = Path("images")
        image_dir.mkdir(exist_ok=True)

        result = {"pages": []}

        with pdfplumber.open(pdf_path) as pdf:
            for page_num, page in enumerate(pdf.pages, start=1):
                logger.info(f"Processing page {page_num}")
                page_data = self._extract_page_content(page, page_num, image_dir)
                result["pages"].append(page_data)

        # Save to JSON
        with open(output_json, "w", encoding="utf-8") as f:
            json.dump(result, f, indent=2, ensure_ascii=False)

        logger.info(f"✅ Extraction completed! Output saved to {output_json}")
        return result

    def _extract_page_content(
        self, page, page_num: int, image_dir: Path
    ) -> Dict[str, Any]:
        """Extract content from a single page"""
        page_data = {"page_number": page_num, "content": []}

        # 1. Extract text content
        text_content = self._extract_text_content(page)
        page_data["content"].extend(text_content)

        # 2. Extract tables
        table_content = self._extract_tables(page)
        page_data["content"].extend(table_content)

        # 3. Extract charts/images
        chart_content = self._extract_charts(page, page_num, image_dir)
        page_data["content"].extend(chart_content)

        return page_data

    def _extract_text_content(self, page) -> List[Dict[str, Any]]:
        """Extract and classify text content"""
        content = []
        text = page.extract_text() or ""

        if not text.strip():
            return content

        # Split into paragraphs
        paragraphs = re.split(r"\n\s*\n", text)

        for para in paragraphs:
            para = para.strip()
            if not para:
                continue

            # Classify the paragraph
            classification = self._classify_text(para)

            content.append(
                {
                    "type": "paragraph",
                    "section": classification.get("section"),
                    "sub_section": classification.get("sub_section"),
                    "text": para,
                }
            )

        return content

    def _classify_text(self, text: str) -> Dict[str, Optional[str]]:
        """Simple text classification to identify sections and subsections"""

        # Check for main sections (usually ALL CAPS or Title Case)
        if self._is_main_section(text):
            self.current_section = text.strip()
            self.current_subsection = None
            return {"section": self.current_section, "sub_section": None}

        # Check for subsections
        if self._is_subsection(text):
            self.current_subsection = text.strip()
            return {
                "section": self.current_section,
                "sub_section": self.current_subsection,
            }

        # Regular paragraph
        return {"section": self.current_section, "sub_section": self.current_subsection}

    def _is_main_section(self, text: str) -> bool:
        """Check if text is a main section header"""
        # Simple heuristics for section headers
        if len(text) > 100:  # Too long to be a header
            return False

        # Check for common section patterns
        section_patterns = [
            r"^[A-Z][A-Z\s&-]+$",  # ALL CAPS
            r"^MONTHLY\s+",
            r"^MARKET\s+UPDATE",
            r"^FACTSHEET",
            r"^\d+\s+ONE\s+.*FUND",  # Fund names
            r"^GLOSSARY$",
            r"^DISCLAIMER$",
        ]

        return any(re.match(pattern, text.strip()) for pattern in section_patterns)

    def _is_subsection(self, text: str) -> bool:
        """Check if text is a subsection header"""
        if len(text) > 80:  # Too long to be a subsection
            return False

        subsection_patterns = [
            r"^[A-Z][a-z]+\s+[A-Z][a-z]+:?$",  # Title Case
            r"^Equity\s+Market",
            r"^Debt\s+Market",
            r"^Portfolio\s+",
            r"^Performance\s+",
            r"^Investment\s+Objective",
            r"^Fund\s+Details",
            r"^Outlook:?$",
        ]

        return any(re.match(pattern, text.strip()) for pattern in subsection_patterns)

    def _extract_tables(self, page) -> List[Dict[str, Any]]:
        """Extract tables from the page"""
        content = []
        tables = page.extract_tables()

        for table in tables:
            if table and len(table) > 0:
                # Clean the table data
                clean_table = []
                for row in table:
                    clean_row = [cell.strip() if cell else "" for cell in row]
                    clean_table.append(clean_row)

                content.append(
                    {
                        "type": "table",
                        "section": self.current_section,
                        "sub_section": self.current_subsection,
                        "description": None,
                        "table_data": clean_table,
                    }
                )

        return content

    def _extract_charts(
        self, page, page_num: int, image_dir: Path
    ) -> List[Dict[str, Any]]:
        """Extract charts/images from the page"""
        content = []

        for i, img in enumerate(page.images, start=1):
            try:
                # Extract image
                bbox = (img["x0"], img["top"], img["x1"], img["bottom"])
                pil_image = page.to_image(resolution=150).original.crop(bbox)

                # Save image
                img_path = image_dir / f"page{page_num}_chart{i}.png"
                pil_image.save(img_path)

                # Try to extract text from image using OCR
                chart_data = self._extract_chart_data(pil_image)

                content.append(
                    {
                        "type": "chart",
                        "section": self.current_section,
                        "sub_section": self.current_subsection,
                        "image_path": str(img_path),
                        "table_data": chart_data,
                        "description": f"Chart extracted from page {page_num}",
                    }
                )

            except Exception as e:
                logger.warning(f"Failed to process chart {i} on page {page_num}: {e}")
                content.append(
                    {
                        "type": "chart",
                        "section": self.current_section,
                        "sub_section": self.current_subsection,
                        "image_path": f"images/page{page_num}_chart{i}.png",
                        "table_data": [],
                        "description": f"Chart extraction failed: {str(e)}",
                    }
                )

        return content

    def _extract_chart_data(self, image: Image.Image) -> List[List[str]]:
        """Extract data from chart image using OCR"""
        try:
            # Use OCR to extract text
            text = pytesseract.image_to_string(image)

            if not text.strip():
                return []

            # Simple parsing to extract potential data points
            lines = text.strip().split("\n")
            chart_data = []

            for line in lines:
                line = line.strip()
                if not line:
                    continue

                # Look for patterns that might be data (numbers, percentages, etc.)
                if re.search(r"\d+", line):
                    # Split by common separators
                    parts = re.split(r"[\s\t]+", line)
                    if len(parts) >= 2:
                        chart_data.append(parts)

            return chart_data[:10]  # Limit to first 10 rows

        except Exception as e:
            logger.warning(f"OCR extraction failed: {e}")
            return []


def main():
    """Main function to run the PDF extractor"""
    extractor = SimplePDFExtractor()

    # Extract PDF content
    pdf_file = "xyz.pdf"  # Change to your PDF file
    output_file = "extracted_simple.json"

    try:
        result = extractor.extract_pdf_to_json(pdf_file, output_file)

        # Print summary
        total_pages = len(result["pages"])
        total_content = sum(len(page["content"]) for page in result["pages"])

        print(f"\n📊 Extraction Summary:")
        print(f"   Total pages: {total_pages}")
        print(f"   Total content blocks: {total_content}")

        # Count by type
        type_counts = {}
        for page in result["pages"]:
            for item in page["content"]:
                item_type = item["type"]
                type_counts[item_type] = type_counts.get(item_type, 0) + 1

        for content_type, count in type_counts.items():
            print(f"   {content_type.title()}s: {count}")

    except Exception as e:
        logger.error(f"Extraction failed: {e}")


if __name__ == "__main__":
    main()
