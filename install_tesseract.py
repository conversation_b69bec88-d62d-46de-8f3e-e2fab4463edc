"""
<PERSON>ript to install Tesseract OCR on Windows
"""
import subprocess
import sys
import os
from pathlib import Path

def install_tesseract():
    """Install Tesseract OCR using winget"""
    try:
        print("Installing Tesseract OCR...")
        result = subprocess.run([
            "winget", "install", "--id", "UB-Mannheim.TesseractOCR", "--accept-package-agreements", "--accept-source-agreements"
        ], capture_output=True, text=True, check=True)
        
        print("✅ Tesseract OCR installed successfully!")
        print("Output:", result.stdout)
        
        # Add Tesseract to PATH
        tesseract_path = r"C:\Program Files\Tesseract-OCR"
        if os.path.exists(tesseract_path):
            current_path = os.environ.get('PATH', '')
            if tesseract_path not in current_path:
                os.environ['PATH'] = f"{tesseract_path};{current_path}"
                print(f"✅ Added {tesseract_path} to PATH")
        
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install Tesseract: {e}")
        print("Error output:", e.stderr)
        return False
    except FileNotFoundError:
        print("❌ winget not found. Please install Tesseract manually from:")
        print("https://github.com/UB-Mannheim/tesseract/wiki")
        return False

def check_tesseract():
    """Check if Tesseract is available"""
    try:
        result = subprocess.run(['tesseract', '--version'], 
                              capture_output=True, text=True, check=True)
        print("✅ Tesseract is available!")
        print("Version:", result.stdout.split('\n')[0])
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("❌ Tesseract not found")
        return False

if __name__ == "__main__":
    print("🔍 Checking for Tesseract OCR...")
    
    if check_tesseract():
        print("Tesseract is already installed and working!")
    else:
        print("Installing Tesseract OCR...")
        if install_tesseract():
            print("\n🔍 Verifying installation...")
            if check_tesseract():
                print("🎉 Tesseract OCR is now ready to use!")
            else:
                print("⚠️  Installation completed but Tesseract may need a system restart to work properly.")
                print("Please restart your command prompt or system and try again.")
        else:
            print("❌ Installation failed. Please install manually.")
